import tkinter as tk
from tkinter import messagebox

def create_error_dialog():
    """直接创建错误对话框，模拟原程序的弹窗"""
    
    # 创建主窗口（模拟app）
    app = tk.Tk()
    app.title("主程序")
    app.geometry("400x300")
    
    def show_error_dialog():
        # 创建对话框
        error_dialog = tk.Toplevel(app)
        error_dialog.title("严重问题 - 多PC属性集无映射")
        error_dialog.geometry("600x400")
        error_dialog.configure(bg="white")
        error_dialog.resizable(False, False)
        error_dialog.transient(app)
        error_dialog.grab_set()
        
        # 居中显示
        error_dialog.update_idletasks()
        x = (error_dialog.winfo_screenwidth() // 2) - 300
        y = (error_dialog.winfo_screenheight() // 2) - 200
        error_dialog.geometry(f"600x400+{x}+{y}")
        
        # 主框架
        main_frame = tk.Frame(error_dialog, bg="white")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # 标题
        title_label = tk.Label(main_frame, text="⚠️ 严重问题", 
                             font=("Microsoft YaHei", 18, "bold"), 
                             fg="red", bg="white")
        title_label.pack(pady=(0, 20))
        
        # 消息
        message_label = tk.Label(main_frame, 
                               text="发现 1 个多PC属性集没有正确的映射关系。\n\n多PC产品必须有正确的属性集映射才能创建文件夹。\n请先更新映射属性集，然后重新运行。", 
                               font=("Microsoft YaHei", 12), 
                               fg="black", bg="white",
                               wraplength=500, justify="left")
        message_label.pack(pady=(0, 30))
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg="white", height=100)
        button_frame.pack(fill=tk.X, pady=20)
        button_frame.pack_propagate(False)
        
        # 按钮回调函数
        def goto_mapping():
            print("用户点击了前往映射表编辑")
            messagebox.showinfo("提示", "前往映射表编辑功能")
            error_dialog.destroy()
            
        def close_dialog():
            print("用户点击了取消运行")
            messagebox.showinfo("提示", "取消运行")
            error_dialog.destroy()
        
        # 前往按钮
        goto_btn = tk.Button(button_frame, text="前往映射表编辑", 
                           font=("Microsoft YaHei", 14, "bold"),
                           bg="green", fg="white", 
                           width=15, height=2,
                           command=goto_mapping)
        goto_btn.pack(side=tk.LEFT, padx=30, pady=20)
        
        # 取消按钮
        cancel_btn = tk.Button(button_frame, text="取消运行", 
                             font=("Microsoft YaHei", 14, "bold"),
                             bg="red", fg="white", 
                             width=15, height=2,
                             command=close_dialog)
        cancel_btn.pack(side=tk.RIGHT, padx=30, pady=20)
        
        print("错误对话框已创建，应该显示两个按钮")
    
    # 主窗口按钮
    test_btn = tk.Button(app, text="显示错误对话框", 
                        font=("Microsoft YaHei", 12),
                        bg="orange", fg="white",
                        width=20, height=3,
                        command=show_error_dialog)
    test_btn.pack(pady=50)
    
    # 启动主循环
    app.mainloop()

if __name__ == "__main__":
    print("启动直接对话框测试...")
    create_error_dialog()
