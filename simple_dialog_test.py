import tkinter as tk

def test_simple_dialog():
    # 创建根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏根窗口
    
    # 创建对话框
    dialog = tk.Toplevel(root)
    dialog.title("测试对话框")
    dialog.geometry("400x200")
    
    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - 200
    y = (dialog.winfo_screenheight() // 2) - 100
    dialog.geometry(f"400x200+{x}+{y}")
    
    # 设置为模态
    dialog.transient(root)
    dialog.grab_set()
    dialog.focus_set()
    
    # 存储结果
    result = [None]
    
    # 添加标签
    tk.Label(dialog, text="这是一个测试对话框", font=("Arial", 14)).pack(pady=20)
    
    # 按钮框架
    button_frame = tk.Frame(dialog)
    button_frame.pack(pady=20)
    
    def on_yes():
        result[0] = True
        print("用户点击了确定")
        dialog.destroy()
        root.destroy()
        
    def on_no():
        result[0] = False
        print("用户点击了取消")
        dialog.destroy()
        root.destroy()
    
    # 创建按钮
    tk.Button(button_frame, text="确定", command=on_yes, 
             bg="green", fg="white", width=10, height=2).pack(side=tk.LEFT, padx=10)
    tk.Button(button_frame, text="取消", command=on_no,
             bg="red", fg="white", width=10, height=2).pack(side=tk.LEFT, padx=10)
    
    # 启动事件循环
    root.mainloop()
    
    return result[0]

if __name__ == "__main__":
    print("开始测试对话框...")
    result = test_simple_dialog()
    print(f"测试结果: {result}")
