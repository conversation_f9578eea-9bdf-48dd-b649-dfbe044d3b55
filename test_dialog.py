import tkinter as tk
from tkinter import messagebox

def test_messagebox():
    # 创建一个隐藏的根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    print("测试1: askokcancel")
    result1 = messagebox.askokcancel("测试", "这是askokcancel测试，应该显示确定和取消按钮")
    print(f"结果1: {result1}")
    
    print("测试2: askyesno")
    result2 = messagebox.askyesno("测试", "这是askyesno测试，应该显示是和否按钮")
    print(f"结果2: {result2}")
    
    print("测试3: askretrycancel")
    result3 = messagebox.askretrycancel("测试", "这是askretrycancel测试，应该显示重试和取消按钮")
    print(f"结果3: {result3}")
    
    print("测试4: showinfo")
    messagebox.showinfo("测试", "这是showinfo测试，应该显示确定按钮")
    
    print("测试5: showerror")
    messagebox.showerror("测试", "这是showerror测试，应该显示确定按钮")
    
    print("测试6: showwarning")
    messagebox.showwarning("测试", "这是showwarning测试，应该显示确定按钮")
    
    root.destroy()

if __name__ == "__main__":
    test_messagebox()
