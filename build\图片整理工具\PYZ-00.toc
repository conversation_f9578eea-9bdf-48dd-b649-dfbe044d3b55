('C:\\Users\\<USER>\\Desktop\\7.24.3\\build\\图片整理工具\\PYZ-00.pyz',
 [('PIL', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageQt.py', 'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageTk.py', 'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\_binary.py', 'PYMODULE'),
  ('PIL._deprecate',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\_typing.py', 'PYMODULE'),
  ('PIL._util', 'D:\\aaaaa\\Lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\aaaaa\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyPDF2', 'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\__init__.py', 'PYMODULE'),
  ('PyPDF2._cmap',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_cmap.py',
   'PYMODULE'),
  ('PyPDF2._codecs',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_codecs\\__init__.py',
   'PYMODULE'),
  ('PyPDF2._codecs.adobe_glyphs',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_codecs\\adobe_glyphs.py',
   'PYMODULE'),
  ('PyPDF2._codecs.pdfdoc',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_codecs\\pdfdoc.py',
   'PYMODULE'),
  ('PyPDF2._codecs.std',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_codecs\\std.py',
   'PYMODULE'),
  ('PyPDF2._codecs.symbol',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_codecs\\symbol.py',
   'PYMODULE'),
  ('PyPDF2._codecs.zapfding',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_codecs\\zapfding.py',
   'PYMODULE'),
  ('PyPDF2._encryption',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_encryption.py',
   'PYMODULE'),
  ('PyPDF2._merger',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_merger.py',
   'PYMODULE'),
  ('PyPDF2._page',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_page.py',
   'PYMODULE'),
  ('PyPDF2._protocols',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_protocols.py',
   'PYMODULE'),
  ('PyPDF2._reader',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_reader.py',
   'PYMODULE'),
  ('PyPDF2._security',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_security.py',
   'PYMODULE'),
  ('PyPDF2._utils',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_utils.py',
   'PYMODULE'),
  ('PyPDF2._version',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_version.py',
   'PYMODULE'),
  ('PyPDF2._writer',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\_writer.py',
   'PYMODULE'),
  ('PyPDF2.constants',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\constants.py',
   'PYMODULE'),
  ('PyPDF2.errors',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\errors.py',
   'PYMODULE'),
  ('PyPDF2.filters',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\filters.py',
   'PYMODULE'),
  ('PyPDF2.generic',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\__init__.py',
   'PYMODULE'),
  ('PyPDF2.generic._annotations',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\_annotations.py',
   'PYMODULE'),
  ('PyPDF2.generic._base',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\_base.py',
   'PYMODULE'),
  ('PyPDF2.generic._data_structures',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\_data_structures.py',
   'PYMODULE'),
  ('PyPDF2.generic._fit',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\_fit.py',
   'PYMODULE'),
  ('PyPDF2.generic._outline',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\_outline.py',
   'PYMODULE'),
  ('PyPDF2.generic._rectangle',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\_rectangle.py',
   'PYMODULE'),
  ('PyPDF2.generic._utils',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\generic\\_utils.py',
   'PYMODULE'),
  ('PyPDF2.pagerange',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\pagerange.py',
   'PYMODULE'),
  ('PyPDF2.papersizes',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\papersizes.py',
   'PYMODULE'),
  ('PyPDF2.types',
   'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\types.py',
   'PYMODULE'),
  ('PyPDF2.xmp', 'D:\\aaaaa\\Lib\\site-packages\\PyPDF2\\xmp.py', 'PYMODULE'),
  ('PyQt5', 'D:\\aaaaa\\Lib\\site-packages\\PyQt5\\__init__.py', 'PYMODULE'),
  ('__future__', 'D:\\aaaaa\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\aaaaa\\Lib\\_aix_support.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\aaaaa\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\aaaaa\\Lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\aaaaa\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\aaaaa\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_markupbase', 'D:\\aaaaa\\Lib\\_markupbase.py', 'PYMODULE'),
  ('_py_abc', 'D:\\aaaaa\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\aaaaa\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\aaaaa\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\aaaaa\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\aaaaa\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\aaaaa\\Lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\aaaaa\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\aaaaa\\Lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\aaaaa\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\aaaaa\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\aaaaa\\Lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\aaaaa\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\aaaaa\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\aaaaa\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\aaaaa\\Lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\aaaaa\\Lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'D:\\aaaaa\\Lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\aaaaa\\Lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'D:\\aaaaa\\Lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\aaaaa\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\aaaaa\\Lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\aaaaa\\Lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\aaaaa\\Lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\aaaaa\\Lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\aaaaa\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'D:\\aaaaa\\Lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\aaaaa\\Lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\aaaaa\\Lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\aaaaa\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\aaaaa\\Lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\aaaaa\\Lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\aaaaa\\Lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\aaaaa\\Lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.taskgroups', 'D:\\aaaaa\\Lib\\asyncio\\taskgroups.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\aaaaa\\Lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\aaaaa\\Lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.timeouts', 'D:\\aaaaa\\Lib\\asyncio\\timeouts.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\aaaaa\\Lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\aaaaa\\Lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\aaaaa\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\aaaaa\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\aaaaa\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\aaaaa\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\aaaaa\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\aaaaa\\Lib\\bisect.py', 'PYMODULE'),
  ('botocore',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\__init__.py',
   'PYMODULE'),
  ('botocore.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\exceptions.py',
   'PYMODULE'),
  ('botocore.vendored',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\vendored\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\vendored\\requests\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\vendored\\requests\\exceptions.py',
   'PYMODULE'),
  ('botocore.vendored.requests.packages',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\vendored\\requests\\packages\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests.packages.urllib3',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\vendored\\requests\\packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('botocore.vendored.requests.packages.urllib3.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\botocore\\vendored\\requests\\packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('brotli', 'D:\\aaaaa\\Lib\\site-packages\\brotli.py', 'PYMODULE'),
  ('bs4', 'D:\\aaaaa\\Lib\\site-packages\\bs4\\__init__.py', 'PYMODULE'),
  ('bs4.builder',
   'D:\\aaaaa\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\aaaaa\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\aaaaa\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\aaaaa\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css', 'D:\\aaaaa\\Lib\\site-packages\\bs4\\css.py', 'PYMODULE'),
  ('bs4.dammit', 'D:\\aaaaa\\Lib\\site-packages\\bs4\\dammit.py', 'PYMODULE'),
  ('bs4.element', 'D:\\aaaaa\\Lib\\site-packages\\bs4\\element.py', 'PYMODULE'),
  ('bs4.formatter',
   'D:\\aaaaa\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\aaaaa\\Lib\\bz2.py', 'PYMODULE'),
  ('cachetools',
   'D:\\aaaaa\\Lib\\site-packages\\cachetools\\__init__.py',
   'PYMODULE'),
  ('cachetools._decorators',
   'D:\\aaaaa\\Lib\\site-packages\\cachetools\\_decorators.py',
   'PYMODULE'),
  ('cachetools.keys',
   'D:\\aaaaa\\Lib\\site-packages\\cachetools\\keys.py',
   'PYMODULE'),
  ('calendar', 'D:\\aaaaa\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\aaaaa\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\aaaaa\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model', 'D:\\aaaaa\\Lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\aaaaa\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\aaaaa\\Lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\aaaaa\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\aaaaa\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\aaaaa\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\aaaaa\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\aaaaa\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\aaaaa\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\aaaaa\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\aaaaa\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\aaaaa\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\aaaaa\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\aaaaa\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\aaaaa\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent', 'D:\\aaaaa\\Lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\aaaaa\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\aaaaa\\Lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\aaaaa\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\aaaaa\\Lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\aaaaa\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\aaaaa\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\aaaaa\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\aaaaa\\Lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\aaaaa\\Lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\aaaaa\\Lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\aaaaa\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\aaaaa\\Lib\\ctypes\\util.py', 'PYMODULE'),
  ('ctypes.wintypes', 'D:\\aaaaa\\Lib\\ctypes\\wintypes.py', 'PYMODULE'),
  ('dataclasses', 'D:\\aaaaa\\Lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\aaaaa\\Lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\aaaaa\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\aaaaa\\Lib\\decimal.py', 'PYMODULE'),
  ('defusedxml',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\aaaaa\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('difflib', 'D:\\aaaaa\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\aaaaa\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\aaaaa\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\aaaaa\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\aaaaa\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\aaaaa\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\aaaaa\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\aaaaa\\Lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\aaaaa\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\aaaaa\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\aaaaa\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\aaaaa\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\aaaaa\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\aaaaa\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\aaaaa\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\aaaaa\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\aaaaa\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\aaaaa\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\aaaaa\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\aaaaa\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\aaaaa\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\aaaaa\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\aaaaa\\Lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'D:\\aaaaa\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\aaaaa\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\aaaaa\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\aaaaa\\Lib\\fractions.py', 'PYMODULE'),
  ('fsspec', 'D:\\aaaaa\\Lib\\site-packages\\fsspec\\__init__.py', 'PYMODULE'),
  ('fsspec._version',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\_version.py',
   'PYMODULE'),
  ('fsspec.asyn', 'D:\\aaaaa\\Lib\\site-packages\\fsspec\\asyn.py', 'PYMODULE'),
  ('fsspec.caching',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\caching.py',
   'PYMODULE'),
  ('fsspec.callbacks',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\callbacks.py',
   'PYMODULE'),
  ('fsspec.compression',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\compression.py',
   'PYMODULE'),
  ('fsspec.config',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\config.py',
   'PYMODULE'),
  ('fsspec.core', 'D:\\aaaaa\\Lib\\site-packages\\fsspec\\core.py', 'PYMODULE'),
  ('fsspec.dircache',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\dircache.py',
   'PYMODULE'),
  ('fsspec.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\exceptions.py',
   'PYMODULE'),
  ('fsspec.implementations',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\implementations\\__init__.py',
   'PYMODULE'),
  ('fsspec.implementations.dirfs',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\implementations\\dirfs.py',
   'PYMODULE'),
  ('fsspec.implementations.local',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\implementations\\local.py',
   'PYMODULE'),
  ('fsspec.json', 'D:\\aaaaa\\Lib\\site-packages\\fsspec\\json.py', 'PYMODULE'),
  ('fsspec.mapping',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\mapping.py',
   'PYMODULE'),
  ('fsspec.registry',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\registry.py',
   'PYMODULE'),
  ('fsspec.spec', 'D:\\aaaaa\\Lib\\site-packages\\fsspec\\spec.py', 'PYMODULE'),
  ('fsspec.transaction',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\transaction.py',
   'PYMODULE'),
  ('fsspec.utils',
   'D:\\aaaaa\\Lib\\site-packages\\fsspec\\utils.py',
   'PYMODULE'),
  ('ftplib', 'D:\\aaaaa\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\aaaaa\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\aaaaa\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\aaaaa\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\aaaaa\\Lib\\glob.py', 'PYMODULE'),
  ('google', '-', 'PYMODULE'),
  ('google.auth',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\__init__.py',
   'PYMODULE'),
  ('google.auth._cloud_sdk',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\_cloud_sdk.py',
   'PYMODULE'),
  ('google.auth._credentials_base',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\_credentials_base.py',
   'PYMODULE'),
  ('google.auth._default',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\_default.py',
   'PYMODULE'),
  ('google.auth._exponential_backoff',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\_exponential_backoff.py',
   'PYMODULE'),
  ('google.auth._helpers',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\_helpers.py',
   'PYMODULE'),
  ('google.auth._refresh_worker',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\_refresh_worker.py',
   'PYMODULE'),
  ('google.auth._service_account_info',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\_service_account_info.py',
   'PYMODULE'),
  ('google.auth.api_key',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\api_key.py',
   'PYMODULE'),
  ('google.auth.app_engine',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\app_engine.py',
   'PYMODULE'),
  ('google.auth.aws',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\aws.py',
   'PYMODULE'),
  ('google.auth.compute_engine',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\compute_engine\\__init__.py',
   'PYMODULE'),
  ('google.auth.compute_engine._metadata',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\compute_engine\\_metadata.py',
   'PYMODULE'),
  ('google.auth.compute_engine.credentials',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\compute_engine\\credentials.py',
   'PYMODULE'),
  ('google.auth.credentials',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\credentials.py',
   'PYMODULE'),
  ('google.auth.crypt',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\crypt\\__init__.py',
   'PYMODULE'),
  ('google.auth.crypt._cryptography_rsa',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\crypt\\_cryptography_rsa.py',
   'PYMODULE'),
  ('google.auth.crypt._python_rsa',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\crypt\\_python_rsa.py',
   'PYMODULE'),
  ('google.auth.crypt.base',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\crypt\\base.py',
   'PYMODULE'),
  ('google.auth.crypt.es256',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\crypt\\es256.py',
   'PYMODULE'),
  ('google.auth.crypt.rsa',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\crypt\\rsa.py',
   'PYMODULE'),
  ('google.auth.environment_vars',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\environment_vars.py',
   'PYMODULE'),
  ('google.auth.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\exceptions.py',
   'PYMODULE'),
  ('google.auth.external_account',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\external_account.py',
   'PYMODULE'),
  ('google.auth.external_account_authorized_user',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\external_account_authorized_user.py',
   'PYMODULE'),
  ('google.auth.iam',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\iam.py',
   'PYMODULE'),
  ('google.auth.identity_pool',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\identity_pool.py',
   'PYMODULE'),
  ('google.auth.impersonated_credentials',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\impersonated_credentials.py',
   'PYMODULE'),
  ('google.auth.jwt',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\jwt.py',
   'PYMODULE'),
  ('google.auth.metrics',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\metrics.py',
   'PYMODULE'),
  ('google.auth.pluggable',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\pluggable.py',
   'PYMODULE'),
  ('google.auth.transport',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\transport\\__init__.py',
   'PYMODULE'),
  ('google.auth.transport._custom_tls_signer',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\transport\\_custom_tls_signer.py',
   'PYMODULE'),
  ('google.auth.transport._http_client',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\transport\\_http_client.py',
   'PYMODULE'),
  ('google.auth.transport._mtls_helper',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\transport\\_mtls_helper.py',
   'PYMODULE'),
  ('google.auth.transport.requests',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\transport\\requests.py',
   'PYMODULE'),
  ('google.auth.version',
   'D:\\aaaaa\\Lib\\site-packages\\google\\auth\\version.py',
   'PYMODULE'),
  ('google.oauth2',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\__init__.py',
   'PYMODULE'),
  ('google.oauth2._client',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\_client.py',
   'PYMODULE'),
  ('google.oauth2.challenges',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\challenges.py',
   'PYMODULE'),
  ('google.oauth2.credentials',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\credentials.py',
   'PYMODULE'),
  ('google.oauth2.gdch_credentials',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\gdch_credentials.py',
   'PYMODULE'),
  ('google.oauth2.reauth',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\reauth.py',
   'PYMODULE'),
  ('google.oauth2.service_account',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\service_account.py',
   'PYMODULE'),
  ('google.oauth2.sts',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\sts.py',
   'PYMODULE'),
  ('google.oauth2.utils',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\utils.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_handler',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\webauthn_handler.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_handler_factory',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\webauthn_handler_factory.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_types',
   'D:\\aaaaa\\Lib\\site-packages\\google\\oauth2\\webauthn_types.py',
   'PYMODULE'),
  ('gzip', 'D:\\aaaaa\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\aaaaa\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\aaaaa\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\aaaaa\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\aaaaa\\Lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'D:\\aaaaa\\Lib\\html\\parser.py', 'PYMODULE'),
  ('http', 'D:\\aaaaa\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\aaaaa\\Lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\aaaaa\\Lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.cookies', 'D:\\aaaaa\\Lib\\http\\cookies.py', 'PYMODULE'),
  ('http.server', 'D:\\aaaaa\\Lib\\http\\server.py', 'PYMODULE'),
  ('idna', 'D:\\aaaaa\\Lib\\site-packages\\idna\\__init__.py', 'PYMODULE'),
  ('idna.core', 'D:\\aaaaa\\Lib\\site-packages\\idna\\core.py', 'PYMODULE'),
  ('idna.idnadata',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\aaaaa\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib', 'D:\\aaaaa\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\aaaaa\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\aaaaa\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\aaaaa\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\aaaaa\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\aaaaa\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\aaaaa\\Lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\aaaaa\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\aaaaa\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\aaaaa\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\aaaaa\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\aaaaa\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('importlib_metadata',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'D:\\aaaaa\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'D:\\aaaaa\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\aaaaa\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.context',
   'D:\\aaaaa\\Lib\\site-packages\\jaraco\\context\\__init__.py',
   'PYMODULE'),
  ('jaraco.functools',
   'D:\\aaaaa\\Lib\\site-packages\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('jinja2', 'D:\\aaaaa\\Lib\\site-packages\\jinja2\\__init__.py', 'PYMODULE'),
  ('jinja2._identifier',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext', 'D:\\aaaaa\\Lib\\site-packages\\jinja2\\ext.py', 'PYMODULE'),
  ('jinja2.filters',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\aaaaa\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\aaaaa\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\aaaaa\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\aaaaa\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\aaaaa\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\aaaaa\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.handlers', 'D:\\aaaaa\\Lib\\logging\\handlers.py', 'PYMODULE'),
  ('lxml', 'D:\\aaaaa\\Lib\\site-packages\\lxml\\__init__.py', 'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\aaaaa\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml_html_clean',
   'D:\\aaaaa\\Lib\\site-packages\\lxml_html_clean\\__init__.py',
   'PYMODULE'),
  ('lxml_html_clean.clean',
   'D:\\aaaaa\\Lib\\site-packages\\lxml_html_clean\\clean.py',
   'PYMODULE'),
  ('lzma', 'D:\\aaaaa\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\aaaaa\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\aaaaa\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\aaaaa\\Lib\\mimetypes.py', 'PYMODULE'),
  ('more_itertools',
   'D:\\aaaaa\\Lib\\site-packages\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('more_itertools.more',
   'D:\\aaaaa\\Lib\\site-packages\\more_itertools\\more.py',
   'PYMODULE'),
  ('more_itertools.recipes',
   'D:\\aaaaa\\Lib\\site-packages\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\aaaaa\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\aaaaa\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\aaaaa\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\aaaaa\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\aaaaa\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\aaaaa\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\aaaaa\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\aaaaa\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\aaaaa\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\aaaaa\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\aaaaa\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\aaaaa\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\aaaaa\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\aaaaa\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\aaaaa\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\aaaaa\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\aaaaa\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\aaaaa\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\aaaaa\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\aaaaa\\Lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\aaaaa\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\aaaaa\\Lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\aaaaa\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas', 'D:\\aaaaa\\Lib\\site-packages\\pandas\\__init__.py', 'PYMODULE'),
  ('pandas._config',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.boxplot',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\boxplot.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.converter',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\converter.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.core',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\core.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.groupby',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\groupby.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.hist',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\hist.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.misc',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\misc.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.style',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\style.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.timeseries',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\timeseries.py',
   'PYMODULE'),
  ('pandas.plotting._matplotlib.tools',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_matplotlib\\tools.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\aaaaa\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\aaaaa\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\aaaaa\\Lib\\pdb.py', 'PYMODULE'),
  ('pdfminer',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\__init__.py',
   'PYMODULE'),
  ('pdfminer._saslprep',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\_saslprep.py',
   'PYMODULE'),
  ('pdfminer.arcfour',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\arcfour.py',
   'PYMODULE'),
  ('pdfminer.ascii85',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\ascii85.py',
   'PYMODULE'),
  ('pdfminer.ccitt',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\ccitt.py',
   'PYMODULE'),
  ('pdfminer.cmapdb',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\cmapdb.py',
   'PYMODULE'),
  ('pdfminer.converter',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\converter.py',
   'PYMODULE'),
  ('pdfminer.data_structures',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\data_structures.py',
   'PYMODULE'),
  ('pdfminer.encodingdb',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\encodingdb.py',
   'PYMODULE'),
  ('pdfminer.fontmetrics',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\fontmetrics.py',
   'PYMODULE'),
  ('pdfminer.glyphlist',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\glyphlist.py',
   'PYMODULE'),
  ('pdfminer.image',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\image.py',
   'PYMODULE'),
  ('pdfminer.jbig2',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\jbig2.py',
   'PYMODULE'),
  ('pdfminer.latin_enc',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\latin_enc.py',
   'PYMODULE'),
  ('pdfminer.layout',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\layout.py',
   'PYMODULE'),
  ('pdfminer.lzw',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\lzw.py',
   'PYMODULE'),
  ('pdfminer.pdfcolor',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdfcolor.py',
   'PYMODULE'),
  ('pdfminer.pdfdevice',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdfdevice.py',
   'PYMODULE'),
  ('pdfminer.pdfdocument',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdfdocument.py',
   'PYMODULE'),
  ('pdfminer.pdffont',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdffont.py',
   'PYMODULE'),
  ('pdfminer.pdfinterp',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdfinterp.py',
   'PYMODULE'),
  ('pdfminer.pdfpage',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdfpage.py',
   'PYMODULE'),
  ('pdfminer.pdfparser',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdfparser.py',
   'PYMODULE'),
  ('pdfminer.pdftypes',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\pdftypes.py',
   'PYMODULE'),
  ('pdfminer.psparser',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\psparser.py',
   'PYMODULE'),
  ('pdfminer.runlength',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\runlength.py',
   'PYMODULE'),
  ('pdfminer.settings',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\settings.py',
   'PYMODULE'),
  ('pdfminer.utils',
   'D:\\aaaaa\\Lib\\site-packages\\pdfminer\\utils.py',
   'PYMODULE'),
  ('pdfplumber',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\__init__.py',
   'PYMODULE'),
  ('pdfplumber._typing',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\_typing.py',
   'PYMODULE'),
  ('pdfplumber._version',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\_version.py',
   'PYMODULE'),
  ('pdfplumber.container',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\container.py',
   'PYMODULE'),
  ('pdfplumber.convert',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\convert.py',
   'PYMODULE'),
  ('pdfplumber.display',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\display.py',
   'PYMODULE'),
  ('pdfplumber.page',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\page.py',
   'PYMODULE'),
  ('pdfplumber.pdf',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\pdf.py',
   'PYMODULE'),
  ('pdfplumber.repair',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\repair.py',
   'PYMODULE'),
  ('pdfplumber.structure',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\structure.py',
   'PYMODULE'),
  ('pdfplumber.table',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\table.py',
   'PYMODULE'),
  ('pdfplumber.utils',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\utils\\__init__.py',
   'PYMODULE'),
  ('pdfplumber.utils.clustering',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\utils\\clustering.py',
   'PYMODULE'),
  ('pdfplumber.utils.generic',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\utils\\generic.py',
   'PYMODULE'),
  ('pdfplumber.utils.geometry',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\utils\\geometry.py',
   'PYMODULE'),
  ('pdfplumber.utils.pdfinternals',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\utils\\pdfinternals.py',
   'PYMODULE'),
  ('pdfplumber.utils.text',
   'D:\\aaaaa\\Lib\\site-packages\\pdfplumber\\utils\\text.py',
   'PYMODULE'),
  ('pickle', 'D:\\aaaaa\\Lib\\pickle.py', 'PYMODULE'),
  ('pickletools', 'D:\\aaaaa\\Lib\\pickletools.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\aaaaa\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\aaaaa\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\aaaaa\\Lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.api',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.version',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'D:\\aaaaa\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'D:\\aaaaa\\Lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\aaaaa\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\aaaaa\\Lib\\py_compile.py', 'PYMODULE'),
  ('pyasn1', 'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\__init__.py', 'PYMODULE'),
  ('pyasn1.codec',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.encoder',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\ber\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.encoder',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\cer\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der.encoder',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\der\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\codec\\streaming.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1.type',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.opentype',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\opentype.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pyasn1_modules',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1_modules\\__init__.py',
   'PYMODULE'),
  ('pyasn1_modules.pem',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1_modules\\pem.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc2251',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1_modules\\rfc2251.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc2459',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1_modules\\rfc2459.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc5208',
   'D:\\aaaaa\\Lib\\site-packages\\pyasn1_modules\\rfc5208.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\aaaaa\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\aaaaa\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\aaaaa\\Lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\aaaaa\\Lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pypdfium2',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\__init__.py',
   'PYMODULE'),
  ('pypdfium2._helpers.attachment',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\attachment.py',
   'PYMODULE'),
  ('pypdfium2._helpers.bitmap',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\bitmap.py',
   'PYMODULE'),
  ('pypdfium2._helpers.document',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\document.py',
   'PYMODULE'),
  ('pypdfium2._helpers.matrix',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\matrix.py',
   'PYMODULE'),
  ('pypdfium2._helpers.misc',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\misc.py',
   'PYMODULE'),
  ('pypdfium2._helpers.page',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\page.py',
   'PYMODULE'),
  ('pypdfium2._helpers.pageobjects',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\pageobjects.py',
   'PYMODULE'),
  ('pypdfium2._helpers.textpage',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\textpage.py',
   'PYMODULE'),
  ('pypdfium2._helpers.unsupported',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_helpers\\unsupported.py',
   'PYMODULE'),
  ('pypdfium2._library_scope',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\_library_scope.py',
   'PYMODULE'),
  ('pypdfium2.internal',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\internal\\__init__.py',
   'PYMODULE'),
  ('pypdfium2.internal.bases',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\internal\\bases.py',
   'PYMODULE'),
  ('pypdfium2.internal.consts',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\internal\\consts.py',
   'PYMODULE'),
  ('pypdfium2.internal.utils',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\internal\\utils.py',
   'PYMODULE'),
  ('pypdfium2.raw',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\raw.py',
   'PYMODULE'),
  ('pypdfium2.version',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2\\version.py',
   'PYMODULE'),
  ('pypdfium2_raw',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2_raw\\__init__.py',
   'PYMODULE'),
  ('pypdfium2_raw.bindings',
   'D:\\aaaaa\\Lib\\site-packages\\pypdfium2_raw\\bindings.py',
   'PYMODULE'),
  ('pyperclip',
   'D:\\aaaaa\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyreadline3',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'D:\\aaaaa\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pytz', 'D:\\aaaaa\\Lib\\site-packages\\pytz\\__init__.py', 'PYMODULE'),
  ('pytz.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy', 'D:\\aaaaa\\Lib\\site-packages\\pytz\\lazy.py', 'PYMODULE'),
  ('pytz.tzfile', 'D:\\aaaaa\\Lib\\site-packages\\pytz\\tzfile.py', 'PYMODULE'),
  ('pytz.tzinfo', 'D:\\aaaaa\\Lib\\site-packages\\pytz\\tzinfo.py', 'PYMODULE'),
  ('queue', 'D:\\aaaaa\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\aaaaa\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\aaaaa\\Lib\\random.py', 'PYMODULE'),
  ('readline', 'D:\\aaaaa\\Lib\\site-packages\\readline.py', 'PYMODULE'),
  ('requests',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\aaaaa\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter', 'D:\\aaaaa\\Lib\\rlcompleter.py', 'PYMODULE'),
  ('rsa', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\__init__.py', 'PYMODULE'),
  ('rsa.asn1', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\asn1.py', 'PYMODULE'),
  ('rsa.common', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\common.py', 'PYMODULE'),
  ('rsa.core', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\core.py', 'PYMODULE'),
  ('rsa.key', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\key.py', 'PYMODULE'),
  ('rsa.parallel',
   'D:\\aaaaa\\Lib\\site-packages\\rsa\\parallel.py',
   'PYMODULE'),
  ('rsa.pem', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\pem.py', 'PYMODULE'),
  ('rsa.pkcs1', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\pkcs1.py', 'PYMODULE'),
  ('rsa.prime', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\prime.py', 'PYMODULE'),
  ('rsa.randnum', 'D:\\aaaaa\\Lib\\site-packages\\rsa\\randnum.py', 'PYMODULE'),
  ('rsa.transform',
   'D:\\aaaaa\\Lib\\site-packages\\rsa\\transform.py',
   'PYMODULE'),
  ('runpy', 'D:\\aaaaa\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\aaaaa\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\aaaaa\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\aaaaa\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\aaaaa\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\aaaaa\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\aaaaa\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\aaaaa\\Lib\\site.py', 'PYMODULE'),
  ('six', 'D:\\aaaaa\\Lib\\site-packages\\six.py', 'PYMODULE'),
  ('smtplib', 'D:\\aaaaa\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\aaaaa\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\aaaaa\\Lib\\socketserver.py', 'PYMODULE'),
  ('socks', 'D:\\aaaaa\\Lib\\site-packages\\socks.py', 'PYMODULE'),
  ('soupsieve',
   'D:\\aaaaa\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\aaaaa\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\aaaaa\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\aaaaa\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\aaaaa\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\aaaaa\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\aaaaa\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\aaaaa\\Lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.__main__', 'D:\\aaaaa\\Lib\\sqlite3\\__main__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\aaaaa\\Lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\aaaaa\\Lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'D:\\aaaaa\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\aaaaa\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\aaaaa\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\aaaaa\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\aaaaa\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\aaaaa\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\aaaaa\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\aaaaa\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\aaaaa\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\aaaaa\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter', 'D:\\aaaaa\\Lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\aaaaa\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants', 'D:\\aaaaa\\Lib\\tkinter\\constants.py', 'PYMODULE'),
  ('tkinter.dialog', 'D:\\aaaaa\\Lib\\tkinter\\dialog.py', 'PYMODULE'),
  ('tkinter.filedialog', 'D:\\aaaaa\\Lib\\tkinter\\filedialog.py', 'PYMODULE'),
  ('tkinter.font', 'D:\\aaaaa\\Lib\\tkinter\\font.py', 'PYMODULE'),
  ('tkinter.messagebox', 'D:\\aaaaa\\Lib\\tkinter\\messagebox.py', 'PYMODULE'),
  ('tkinter.scrolledtext',
   'D:\\aaaaa\\Lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\aaaaa\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk', 'D:\\aaaaa\\Lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('token', 'D:\\aaaaa\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\aaaaa\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib', 'D:\\aaaaa\\Lib\\tomllib\\__init__.py', 'PYMODULE'),
  ('tomllib._parser', 'D:\\aaaaa\\Lib\\tomllib\\_parser.py', 'PYMODULE'),
  ('tomllib._re', 'D:\\aaaaa\\Lib\\tomllib\\_re.py', 'PYMODULE'),
  ('tomllib._types', 'D:\\aaaaa\\Lib\\tomllib\\_types.py', 'PYMODULE'),
  ('tqdm', 'D:\\aaaaa\\Lib\\site-packages\\tqdm\\__init__.py', 'PYMODULE'),
  ('tqdm._dist_ver',
   'D:\\aaaaa\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'D:\\aaaaa\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'D:\\aaaaa\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli', 'D:\\aaaaa\\Lib\\site-packages\\tqdm\\cli.py', 'PYMODULE'),
  ('tqdm.gui', 'D:\\aaaaa\\Lib\\site-packages\\tqdm\\gui.py', 'PYMODULE'),
  ('tqdm.notebook',
   'D:\\aaaaa\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std', 'D:\\aaaaa\\Lib\\site-packages\\tqdm\\std.py', 'PYMODULE'),
  ('tqdm.utils', 'D:\\aaaaa\\Lib\\site-packages\\tqdm\\utils.py', 'PYMODULE'),
  ('tqdm.version',
   'D:\\aaaaa\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\aaaaa\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('ttkbootstrap',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.colorutils',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\colorutils.py',
   'PYMODULE'),
  ('ttkbootstrap.constants',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\constants.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\dialogs\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colorchooser',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\dialogs\\colorchooser.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colordropper',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\dialogs\\colordropper.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.dialogs',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\dialogs\\dialogs.py',
   'PYMODULE'),
  ('ttkbootstrap.icons',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\icons.py',
   'PYMODULE'),
  ('ttkbootstrap.localization',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\localization\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgcat',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\localization\\msgcat.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgs',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\localization\\msgs.py',
   'PYMODULE'),
  ('ttkbootstrap.publisher',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\publisher.py',
   'PYMODULE'),
  ('ttkbootstrap.style',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\style.py',
   'PYMODULE'),
  ('ttkbootstrap.themes',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\themes\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.standard',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\themes\\standard.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.user',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\themes\\user.py',
   'PYMODULE'),
  ('ttkbootstrap.tooltip',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\tooltip.py',
   'PYMODULE'),
  ('ttkbootstrap.utility',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\utility.py',
   'PYMODULE'),
  ('ttkbootstrap.validation',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\validation.py',
   'PYMODULE'),
  ('ttkbootstrap.widgets',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\widgets.py',
   'PYMODULE'),
  ('ttkbootstrap.window',
   'D:\\aaaaa\\Lib\\site-packages\\ttkbootstrap\\window.py',
   'PYMODULE'),
  ('tty', 'D:\\aaaaa\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\aaaaa\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\aaaaa\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\aaaaa\\Lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\aaaaa\\Lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\aaaaa\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\aaaaa\\Lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\aaaaa\\Lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\aaaaa\\Lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\aaaaa\\Lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'D:\\aaaaa\\Lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\aaaaa\\Lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\aaaaa\\Lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\aaaaa\\Lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\aaaaa\\Lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\aaaaa\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\aaaaa\\Lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\aaaaa\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\aaaaa\\Lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\aaaaa\\Lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib3',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports.weakref_finalize',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\backports\\weakref_finalize.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\aaaaa\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\aaaaa\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\aaaaa\\Lib\\webbrowser.py', 'PYMODULE'),
  ('wheel', 'D:\\aaaaa\\Lib\\site-packages\\wheel\\__init__.py', 'PYMODULE'),
  ('wheel._setuptools_logging',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('wheel.bdist_wheel',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\bdist_wheel.py',
   'PYMODULE'),
  ('wheel.cli',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util', 'D:\\aaaaa\\Lib\\site-packages\\wheel\\util.py', 'PYMODULE'),
  ('wheel.vendored',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'D:\\aaaaa\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32con',
   'D:\\aaaaa\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\aaaaa\\Lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\aaaaa\\Lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'D:\\aaaaa\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\aaaaa\\Lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter', 'D:\\aaaaa\\Lib\\xml\\dom\\NodeFilter.py', 'PYMODULE'),
  ('xml.dom.domreg', 'D:\\aaaaa\\Lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\aaaaa\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat', 'D:\\aaaaa\\Lib\\xml\\dom\\minicompat.py', 'PYMODULE'),
  ('xml.dom.minidom', 'D:\\aaaaa\\Lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'D:\\aaaaa\\Lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.xmlbuilder', 'D:\\aaaaa\\Lib\\xml\\dom\\xmlbuilder.py', 'PYMODULE'),
  ('xml.etree', 'D:\\aaaaa\\Lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\aaaaa\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\aaaaa\\Lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\aaaaa\\Lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'D:\\aaaaa\\Lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\aaaaa\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\aaaaa\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\aaaaa\\Lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\aaaaa\\Lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\aaaaa\\Lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\aaaaa\\Lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\aaaaa\\Lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc.server', 'D:\\aaaaa\\Lib\\xmlrpc\\server.py', 'PYMODULE'),
  ('zipfile', 'D:\\aaaaa\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path', 'D:\\aaaaa\\Lib\\zipfile\\_path\\__init__.py', 'PYMODULE'),
  ('zipfile._path.glob', 'D:\\aaaaa\\Lib\\zipfile\\_path\\glob.py', 'PYMODULE'),
  ('zipimport', 'D:\\aaaaa\\Lib\\zipimport.py', 'PYMODULE'),
  ('zipp', 'D:\\aaaaa\\Lib\\site-packages\\zipp\\__init__.py', 'PYMODULE'),
  ('zipp._functools',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'D:\\aaaaa\\Lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob', 'D:\\aaaaa\\Lib\\site-packages\\zipp\\glob.py', 'PYMODULE'),
  ('zstandard',
   'D:\\aaaaa\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\aaaaa\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
