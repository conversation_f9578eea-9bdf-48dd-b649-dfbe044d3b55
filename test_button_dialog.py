import tkinter as tk
from tkinter import messagebox

def test_button_dialog():
    # 创建主窗口
    root = tk.Tk()
    root.title("测试主窗口")
    root.geometry("300x200")
    
    def show_test_dialog():
        # 创建测试对话框
        dialog = tk.Toplevel(root)
        dialog.title("测试对话框")
        dialog.geometry("400x300")
        dialog.configure(bg="white")
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - 200
        y = (dialog.winfo_screenheight() // 2) - 150
        dialog.geometry(f"400x300+{x}+{y}")
        
        # 主框架
        main_frame = tk.Frame(dialog, bg="white")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = tk.Label(main_frame, text="测试对话框", 
                             font=("Microsoft YaHei", 16, "bold"), 
                             fg="blue", bg="white")
        title_label.pack(pady=20)
        
        # 消息
        message_label = tk.Label(main_frame, text="这是一个测试对话框\n请检查下面是否有两个按钮", 
                               font=("Microsoft YaHei", 12), 
                               fg="black", bg="white")
        message_label.pack(pady=20)
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg="white", height=80)
        button_frame.pack(fill=tk.X, pady=20)
        button_frame.pack_propagate(False)
        
        def on_button1():
            messagebox.showinfo("按钮1", "你点击了按钮1")
            
        def on_button2():
            messagebox.showinfo("按钮2", "你点击了按钮2")
            dialog.destroy()
        
        # 按钮1
        button1 = tk.Button(button_frame, text="按钮1 (绿色)", 
                          font=("Microsoft YaHei", 12, "bold"),
                          bg="green", fg="white", 
                          width=12, height=2,
                          command=on_button1)
        button1.pack(side=tk.LEFT, padx=20, pady=10)
        
        # 按钮2
        button2 = tk.Button(button_frame, text="按钮2 (红色)", 
                          font=("Microsoft YaHei", 12, "bold"),
                          bg="red", fg="white", 
                          width=12, height=2,
                          command=on_button2)
        button2.pack(side=tk.RIGHT, padx=20, pady=10)
        
        print("测试对话框已创建，应该显示两个按钮")
    
    # 主窗口按钮
    test_btn = tk.Button(root, text="显示测试对话框", 
                        font=("Microsoft YaHei", 12),
                        command=show_test_dialog)
    test_btn.pack(pady=50)
    
    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    test_button_dialog()
