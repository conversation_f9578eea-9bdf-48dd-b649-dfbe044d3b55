
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional), posixpath (optional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named pyimod02_importers - imported by D:\aaaaa\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed), D:\aaaaa\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), netrc (delayed, conditional), getpass (delayed), setuptools._vendor.backports.tarfile (optional), http.server (delayed, optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), xmlrpc.server (optional), tqdm.utils (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), tqdm.utils (delayed, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named startup - imported by pyreadline3.keysyms.common (conditional), pyreadline3.keysyms.keysyms (conditional)
missing module named sets - imported by pytz.tzinfo (optional), pyreadline3.keysyms.common (optional)
missing module named System - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.keysyms.ironpython_keysyms (top-level), pyreadline3.console.ironpython_console (top-level), pyreadline3.rlmain (conditional)
missing module named console - imported by pyreadline3.console.ansi (conditional)
missing module named clr - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.console.ironpython_console (top-level)
missing module named IronPythonConsole - imported by pyreadline3.console.ironpython_console (top-level)
missing module named _typeshed - imported by setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named packaging.licenses - imported by packaging (optional), setuptools.config._validate_pyproject.formats (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named 'packaging.licenses' - imported by setuptools._normalization (optional)
missing module named 'numpy.typing' - imported by pandas._typing (conditional), PIL._typing (optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
excluded module named numpy - imported by pandas.compat.numpy (top-level), pandas.core.util.hashing (top-level), pandas.core.dtypes.common (top-level), pandas.core.dtypes.base (top-level), pandas._typing (top-level), pandas.core.dtypes.dtypes (top-level), pandas.core.arrays.arrow.array (top-level), pandas.util._validators (top-level), pandas.core.missing (top-level), pandas.core.dtypes.cast (top-level), pandas.core.dtypes.inference (top-level), pandas.core.dtypes.missing (top-level), pandas.core.indexes.base (top-level), pandas.compat.numpy.function (top-level), pandas.core.dtypes.astype (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.core.arrays.base (top-level), pandas.core.arraylike (top-level), pandas.core.ops.array_ops (top-level), pandas.core.computation.expressions (top-level), pandas.core.ops.missing (top-level), pandas.core.ops.invalid (top-level), pandas.core.ops.mask_ops (top-level), pandas.core.frame (top-level), pandas.core.dtypes.concat (top-level), pandas.core.arrays.categorical (top-level), pandas.core.algorithms (top-level), pandas.core.array_algos.take (top-level), pandas.core.arrays._mixins (top-level), pandas.core.array_algos.quantile (top-level), pandas.core.array_algos.transforms (top-level), pandas.core.indexers.utils (top-level), pandas.core.sorting (top-level), pandas.core.indexes.api (top-level), pandas.core.indexes.category (top-level), pandas.core.indexes.extension (conditional), pandas.core.indexes.datetimes (top-level), pandas.core.arrays.datetimes (top-level), pandas.core.arrays.datetimelike (top-level), pandas.core.nanops (top-level), pandas.core.array_algos.datetimelike_accumulations (top-level), pandas.core.arrays.integer (top-level), pandas.core.arrays.numeric (top-level), pandas.core.arrays.masked (top-level), pandas.core.array_algos.masked_accumulations (top-level), pandas.core.array_algos.masked_reductions (top-level), pandas.core.arrays._utils (top-level), pandas.core.groupby.generic (top-level), pandas.core.apply (top-level), pandas.core._numba.executor (top-level), pandas.core.resample (top-level), pandas.core.base (top-level), pandas.core.generic (top-level), pandas.core.indexing (top-level), pandas.core.sample (top-level), pandas.core.array_algos.replace (top-level), pandas.core.internals.api (top-level), pandas.core.internals.blocks (top-level), pandas.core.array_algos.putmask (top-level), pandas.core.arrays.boolean (top-level), pandas.core.arrays.floating (top-level), pandas.core.arrays.string_ (top-level), pandas.core.arrays.numpy_ (top-level), pandas.core.strings.object_array (top-level), pandas.core.strings.base (top-level), pandas.core.arrays.string_arrow (top-level), pandas.core.arrays._arrow_string_mixins (top-level), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.indexes.interval (top-level), pandas.core.arrays.interval (top-level), pandas.core.arrays.timedeltas (top-level), pandas.core.arrays._ranges (top-level), pandas.io.formats.style (top-level), pandas.io.formats.format (top-level), pandas.core.indexes.datetimelike (top-level), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (top-level), pandas.core.reshape.concat (top-level), pandas.core.indexes.multi (top-level), pandas.core.reshape.util (top-level), pandas.io.formats.string (top-level), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.indexers.objects (top-level), pandas.tseries.frequencies (top-level), pandas.core.interchange.from_dataframe (top-level), pandas.core.interchange.utils (top-level), pandas.core.window.ewm (top-level), pandas.core.util.numba_ (top-level), pandas.core.window.common (top-level), pandas.core.window.numba_ (top-level), pandas.core.window.online (top-level), pandas.core.window.rolling (top-level), pandas.core.groupby.ops (top-level), pandas.core.groupby.grouper (top-level), pandas.core.groupby.categorical (top-level), pandas.core.series (top-level), pandas.core.arrays.sparse.accessor (top-level), pandas.core.arrays.sparse.array (top-level), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.indexes.accessors (top-level), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (top-level), pandas.core.tools.datetimes (top-level), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.reshape.reshape (top-level), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (conditional), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.io.json._json (top-level), pandas.io.json._normalize (top-level), pandas.io.parsers.readers (top-level), pandas.io.parsers.base_parser (top-level), pandas.io.parsers.c_parser_wrapper (top-level), pandas.io.parsers.python_parser (top-level), pandas.io.stata (top-level), pandas.io.formats.excel (top-level), pandas.io.excel._odfreader (top-level), pandas.io.excel._openpyxl (top-level), openpyxl.compat.numbers (optional), PIL.Image (delayed, conditional, optional), pandas.io.excel._xlrd (top-level), pandas.core.indexes.period (top-level), pandas.core.arrays.period (top-level), pandas.core.tools.numeric (top-level), pandas.core.internals.managers (top-level), pandas.core.internals.base (top-level), pandas.core.internals.array_manager (top-level), pandas.core.internals.concat (top-level), pandas.core.internals.construction (top-level), pandas.core.methods.describe (top-level), pandas.io.pytables (top-level), pandas.compat.pickle_compat (top-level), pandas.core.computation.pytables (top-level), pandas.core.computation.expr (top-level), pandas.core.computation.ops (top-level), pandas.core.computation.common (top-level), pandas.core.computation.scope (top-level), pandas.core.computation.align (top-level), pandas.io.sql (top-level), pandas.core.groupby.groupby (top-level), pandas.core.groupby.numba_ (top-level), pandas.core.groupby.indexing (top-level), pandas.core._numba.extensions (top-level), pandas.core.reshape.merge (top-level), pandas.core.reshape.tile (top-level), pandas.core.tools.times (top-level), pandas.core.reshape.melt (top-level), pandas.core.interchange.column (top-level), pandas.core.interchange.buffer (conditional), pandas.core.methods.to_dict (top-level), pandas.core.reshape.pivot (top-level), pandas.tseries.holiday (top-level), pandas.core.reshape.encoding (top-level), pandas._testing (top-level), pandas._testing.asserters (top-level), pandas.io.sas.sas_xport (top-level), pandas.io.sas.sas7bdat (top-level), pypdfium2._helpers.bitmap (optional), pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.groupby (top-level), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.timeseries (top-level), pandas.plotting._matplotlib.hist (top-level)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional)
missing module named _dummy_thread - imported by cffi.lock (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named ttkbootstrap.dialogs.Dialog - imported by ttkbootstrap.dialogs (top-level), ttkbootstrap.dialogs.colorchooser (top-level)
excluded module named pygame - imported by pdfminer.ccitt (delayed)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named StringIO - imported by six (conditional), urllib3.packages.six (conditional)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), urllib3.packages.six.moves.urllib (top-level), urllib3.util.queue (top-level)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named Queue - imported by urllib3.util.queue (conditional)
missing module named 'urllib3.packages.six.moves.urllib.parse' - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named 'OpenSSL.SSL' - imported by urllib3.contrib.pyopenssl (top-level)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional), google.auth.identity_pool (delayed)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional), pyperclip (delayed, conditional, optional)
missing module named 'qtpy.QtWidgets' - imported by pyperclip (delayed, optional)
missing module named 'Crypto.Util' - imported by PyPDF2._encryption (optional)
missing module named Crypto - imported by PyPDF2._encryption (optional)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional)
missing module named pyarrow - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.utils (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional)
missing module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
excluded module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named UserDict - imported by pytz.lazy (optional)
excluded module named scipy - imported by pandas.core.missing (delayed)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named bs4.builder.HTMLParserTreeBuilder - imported by bs4.builder (top-level), bs4 (top-level)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (optional), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional), tqdm.gui (delayed), pandas.plotting._matplotlib.tools (delayed), pandas.plotting._matplotlib.style (delayed), pandas.plotting._matplotlib.misc (delayed), pandas.plotting._matplotlib.core (delayed), pandas.plotting._matplotlib.boxplot (delayed), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib (delayed)
excluded module named matplotlib - imported by pandas.io.formats.style (optional), tqdm.gui (delayed), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.timeseries (delayed)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'numpy.ma' - imported by pandas.core.frame (delayed, conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named 'matplotlib.colors' - imported by pandas.plotting._misc (conditional), pandas.io.formats.style (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.plotting._matplotlib.hist (delayed)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.axes' - imported by pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas._testing.asserters (delayed), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (delayed, conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.lines' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.boxplot (conditional)
missing module named 'matplotlib.ticker' - imported by pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'matplotlib.axis' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.artist' - imported by pandas._testing.asserters (delayed), pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.units' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.transforms' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.dates' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.table' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (top-level)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed), pandas.core.dtypes.common (delayed, conditional, optional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed)
